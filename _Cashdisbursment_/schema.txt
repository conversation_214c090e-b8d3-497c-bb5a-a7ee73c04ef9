Database: ZimdefCashDisbursement
Tables:
Acquittals
AcquittalID (Primary Key, int)
ApplicationID (Foreign Key, int, references Applications)

AcquittalSubmissions
SubmissionID (Primary Key, int)
AcquittalID (Foreign Key, int, references Acquittals)
Description (varchar, 500)
Amount (float)
PDFLocation (varchar, 500)
Date (datetime)
SubmittedBy (varchar, 255)

Applications
ApplicationID (Primary Key, int)
CompanyID (Foreign Key, int, references Company)
Description (varchar, 500)
Purpose (varchar, 500)
RequestedCash (float)
DisbursedCash (float, nullable)
IsDisbursed (bit)
DisbursedBy (varchar, 255)
DateRequested (datetime)
DateDisbursed (datetime)
Status (varchar, 50)
ApprovalLevel (int)
ApprovalComment (varchar, 500)
PDFLocation (varchar, 500)


ApprovalList
id (Primary Key, int)
email (varchar, 255, unique)
approverNum (int)
Company

CompanyID (Primary Key, int)
Name (varchar, 255)
Email (varchar, 255, unique)
Contact (varchar, 15)
Address (varchar, 500)
Status (bit)
Logs

LogID (Primary Key, int)
Description (varchar, 500)
Date (datetime)
UserID (Foreign Key, int, references Users)
Users

UserID (Primary Key, int)
Email (varchar, 255, unique)
Password (varchar, 255)
Status (bit)
CompanyID (Foreign Key, int, references Company)
Role (varchar, 50)
Foreign Key Relationships:
Acquittals.ApplicationID references Applications.ApplicationID
AcquittalSubmissions.AcquittalID references Acquittals.AcquittalID
Applications.CompanyID references Company.CompanyID
Logs.UserID references Users.UserID
Users.CompanyID references Company.CompanyID