/* Custom CSS for Zimdef Cash Disbursement System - Professional Minimal Design */

:root {
  --primary-color: #2c3e50;
  --secondary-color: #34495e;
  --accent-color: #3498db;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --light-bg: #f8f9fa;
  --white-bg: #ffffff;
  --dark-text: #2c3e50;
  --muted-text: #6c757d;
  --border-color: #dee2e6;
  --subtle-border: #e9ecef;
  --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.08),
    0 1px 2px 0 rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 3px 6px -1px rgba(0, 0, 0, 0.08),
    0 2px 4px -1px rgba(0, 0, 0, 0.04);
  --shadow-large: 0 8px 15px -3px rgba(0, 0, 0, 0.08),
    0 4px 6px -2px rgba(0, 0, 0, 0.04);
}

/* Global Styles */
body {
  font-family: "Inter", "Segoe UI", -apple-system, BlinkMacSystemFont,
    sans-serif;
  background-color: var(--light-bg);
  color: var(--dark-text);
  line-height: 1.5;
  font-size: 14px;
  letter-spacing: -0.01em;
}

/* Z-index hierarchy for proper layering */
.navbar {
  z-index: 1050 !important;
}

.dropdown-menu {
  z-index: 1060 !important;
}

.modal {
  z-index: 1070 !important;
}

.modal-backdrop {
  z-index: 1065 !important;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  color: var(--dark-text);
  letter-spacing: -0.02em;
  margin-bottom: 0.5rem;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-weight: 600;
  color: var(--dark-text);
  letter-spacing: -0.02em;
}

/* Layout Containers */
.container,
.container-fluid {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.main-content {
  min-height: calc(100vh - 100px);
  padding: 1rem 0;
}

/* Enhanced content spacing for better visual breathing room */
.content-wrapper {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Page content containers */
.page-content {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

/* Professional Navigation */
.navbar {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%) !important;
  border-bottom: 1px solid rgba(52, 152, 219, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0.75rem 0;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1050;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  color: #ffffff !important;
  letter-spacing: -0.02em;
  text-decoration: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.navbar-brand:hover {
  color: #3498db !important;
  text-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.navbar-logo {
  height: 32px;
  width: auto;
  filter: brightness(0) invert(1);
  transition: all 0.3s ease;
}

.navbar-brand:hover .navbar-logo {
  filter: brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(175deg);
}

.navbar-nav .nav-link {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9) !important;
  padding: 0.6rem 1rem !important;
  margin: 0 0.25rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.navbar-nav .nav-link:hover {
  color: #ffffff !important;
  background: rgba(52, 152, 219, 0.2);
  transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
  color: #ffffff !important;
  background: rgba(52, 152, 219, 0.3);
}

.dropdown-menu {
  border: 1px solid var(--border-color);
  background: var(--white-bg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  z-index: 1060;
  position: absolute;
}

.dropdown-item {
  padding: 0.6rem 1.25rem;
  font-size: 0.9rem;
  color: var(--dark-text);
  transition: all 0.3s ease;
  font-weight: 500;
}

.dropdown-item:hover {
  background: var(--light-bg);
  color: var(--accent-color);
}

.dropdown-divider {
  margin: 0.5rem 1rem;
  border-color: var(--border-color);
  opacity: 0.3;
}

/* Navbar toggler for mobile */
.navbar-toggler {
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.4rem 0.6rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.navbar-toggler:hover {
  border-color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.3);
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Card Styles */
.card {
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  border-radius: 8px;
  margin-bottom: 1rem;
  background-color: var(--white-bg);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-medium);
}

.card-header {
  background-color: var(--white-bg);
  color: var(--dark-text);
  border-bottom: 1px solid var(--border-color);
  border-radius: 8px 8px 0 0 !important;
  font-weight: 600;
  font-size: 0.95rem;
  padding: 1rem 1.25rem;
}

.card-body {
  padding: 1.25rem;
}

/* Button Styles */
.btn {
  font-weight: 500;
  border-radius: 8px;
  padding: 0.625rem 1.25rem;
  font-size: 0.9rem;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  letter-spacing: -0.01em;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #2f855a;
  border-color: #2f855a;
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background-color: #b7791f;
  border-color: #b7791f;
  color: white;
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #c53030;
  border-color: #c53030;
  color: white;
}

.btn-outline-secondary {
  color: var(--muted-text);
  border-color: var(--border-color);
  background-color: transparent;
}

.btn-outline-secondary:hover {
  background-color: var(--subtle-border);
  border-color: var(--border-color);
  color: var(--dark-text);
}

/* Form Styles */
.form-control {
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  background-color: var(--white-bg);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(26, 26, 26, 0.1);
  outline: none;
}

.form-label {
  font-weight: 600;
  color: var(--dark-text);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-text {
  font-size: 0.8rem;
  color: var(--muted-text);
  margin-top: 0.25rem;
}

/* Table Styles */
.table {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
}

.table thead th {
  background-color: var(--primary-color);
  color: white;
  border: none;
  font-weight: 500;
  padding: 15px;
}

.table tbody td {
  padding: 12px 15px;
  vertical-align: middle;
  border-top: 1px solid #f1f3f4;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Status Badges */
.status-pending {
  background-color: var(--warning-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
  font-weight: 500;
}

.status-approved {
  background-color: var(--success-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
  font-weight: 500;
}

.status-rejected {
  background-color: var(--danger-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
  font-weight: 500;
}

.status-disbursed {
  background-color: #6c757d;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
  font-weight: 500;
}

/* Dashboard Cards - Compact Professional Design */
.dashboard-card {
  background: var(--white-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--accent-color);
}

.dashboard-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-1px);
}

.dashboard-card .card-body {
  padding: 0;
}

.dashboard-card h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.125rem;
  color: var(--primary-color);
}

.dashboard-card p {
  margin-bottom: 0;
  color: var(--muted-text);
  font-size: 0.875rem;
  font-weight: 500;
}

.dashboard-card small {
  color: var(--muted-text);
  font-size: 0.75rem;
}

.dashboard-card i {
  margin-bottom: 0.5rem;
}

/* Login/Register Forms */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--light-bg);
  padding: 2rem 1rem;
}

.auth-card {
  background: var(--white-bg);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 3rem 2.5rem;
  box-shadow: var(--shadow-large);
  width: 100%;
  max-width: 420px;
  position: relative;
}

.auth-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 16px 16px 0 0;
}

.auth-card h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--primary-color);
  font-weight: 700;
  font-size: 1.75rem;
}

/* Sidebar Navigation */
.sidebar {
  background-color: var(--primary-color);
  min-height: calc(100vh - 56px);
  padding: 20px 0;
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 12px 20px;
  border-radius: 0;
  transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

/* File Upload */
.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.file-upload-area:hover {
  border-color: var(--secondary-color);
  background-color: rgba(52, 152, 219, 0.05);
}

/* Page Headers - Simple and Compact */
.page-header {
  padding: 0.75rem 0;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.page-header h1,
.page-header .h1,
.page-header h2,
.page-header .h2,
.page-header h3,
.page-header .h3 {
  margin-bottom: 0.25rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--dark-text);
}

.page-header p {
  margin-bottom: 0;
  color: var(--muted-text);
  font-size: 0.875rem;
}

/* Accordion Styles for Application Organization */
.applications-accordion .accordion-item {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.applications-accordion .accordion-header {
  background: var(--white-bg);
}

.applications-accordion .accordion-button {
  background: var(--white-bg);
  color: var(--dark-text);
  font-weight: 600;
  padding: 1rem 1.25rem;
  border: none;
  box-shadow: none;
}

.applications-accordion .accordion-button:not(.collapsed) {
  background: var(--light-bg);
  color: var(--primary-color);
}

.applications-accordion .accordion-button:focus {
  box-shadow: none;
  border: none;
}

.applications-accordion .accordion-body {
  padding: 0;
}

.applications-accordion .table-container {
  max-height: 400px;
  overflow-y: auto;
  border-top: 1px solid var(--border-color);
}

.applications-accordion .table {
  margin-bottom: 0;
}

.applications-accordion .table th {
  position: sticky;
  top: 0;
  background: var(--white-bg);
  z-index: 10;
  border-bottom: 2px solid var(--border-color);
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.75rem;
}

.applications-accordion .table td {
  font-size: 0.875rem;
  padding: 0.75rem;
  vertical-align: middle;
}

/* Status Badge Styles */
.status-indicator {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-pending {
  background: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(243, 156, 18, 0.2);
}

.status-approved {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(39, 174, 96, 0.2);
}

.status-rejected {
  background: rgba(231, 76, 60, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.status-disbursed {
  background: rgba(52, 152, 219, 0.1);
  color: var(--accent-color);
  border: 1px solid rgba(52, 152, 219, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-card {
    margin: 20px;
    padding: 30px 20px;
  }

  .dashboard-card h3 {
    font-size: 1.5rem;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .applications-accordion .table th,
  .applications-accordion .table td {
    font-size: 0.8rem;
    padding: 0.5rem;
  }

  .page-header h1,
  .page-header .h1,
  .page-header h2,
  .page-header .h2,
  .page-header h3,
  .page-header .h3 {
    font-size: 1.1rem;
  }

  /* Hero Section Mobile Fixes */
  .hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
  }

  .hero-content .lead {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .hero-icon-container {
    width: 200px;
    height: 200px;
    margin-bottom: 2rem;
  }

  .hero-icon-container i {
    font-size: 4rem;
  }

  .hero-section .btn {
    padding: 0.75rem 2rem;
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  /* Stack hero content vertically on mobile */
  .hero-section .row {
    flex-direction: column-reverse;
  }

  .hero-section .col-lg-6:first-child {
    margin-top: 2rem;
  }
}

/* Animation */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Alert Styles */
.alert {
  border-radius: 8px;
  border: none;
  padding: 15px 20px;
}

.alert-success {
  background-color: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
  border-left: 4px solid var(--success-color);
}

.alert-danger {
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--danger-color);
  border-left: 4px solid var(--danger-color);
}

.alert-warning {
  background-color: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
  border-left: 4px solid var(--warning-color);
}

/* Loading Spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Footer */
.footer {
  background-color: var(--primary-color);
  color: white;
  padding: 20px 0;
  margin-top: 50px;
}

.footer p {
  margin-bottom: 0;
  text-align: center;
}

/* Magnificent Hero Section */
.hero-section {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #1e3c72 100%);
  color: white;
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(52, 152, 219, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(155, 89, 182, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(46, 204, 113, 0.2) 0%, transparent 50%);
  animation: heroGlow 8s ease-in-out infinite alternate;
}

.hero-section::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M50 50m-25 0a25 25 0 1 1 50 0a25 25 0 1 1 -50 0'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.4;
  animation: heroPattern 20s linear infinite;
}

@keyframes heroGlow {
  0% { opacity: 0.8; transform: scale(1); }
  100% { opacity: 1; transform: scale(1.05); }
}

@keyframes heroPattern {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(-100px) translateY(-100px); }
}

.min-vh-75 {
  min-height: 75vh;
}

.hero-content {
  position: relative;
  z-index: 3;
  animation: heroSlideIn 1.2s ease-out;
}

@keyframes heroSlideIn {
  0% { opacity: 0; transform: translateY(50px); }
  100% { opacity: 1; transform: translateY(0); }
}

.hero-content h1 {
  font-weight: 900;
  font-size: 4rem;
  margin-bottom: 2rem;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #ffffff 0%, #3498db 50%, #9b59b6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.hero-content .lead {
  font-size: 1.4rem;
  opacity: 0.95;
  line-height: 1.7;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 3rem;
  color: rgba(255, 255, 255, 0.9);
}

.hero-visual {
  position: relative;
  z-index: 3;
  animation: heroFloat 6s ease-in-out infinite;
}

@keyframes heroFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.hero-icon-container {
  width: 300px;
  height: 300px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.2) 0%, rgba(155, 89, 182, 0.2) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(20px);
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    inset 0 0 50px rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.hero-icon-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: heroIconShine 3s linear infinite;
}

@keyframes heroIconShine {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.hero-icon-container i {
  font-size: 6rem;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  z-index: 2;
  position: relative;
}

/* Enhanced Buttons for Hero Section */
.hero-section .btn {
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hero-section .btn-light {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: none;
  color: var(--primary-color);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.hero-section .btn-light:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(255, 255, 255, 0.4);
  background: linear-gradient(135deg, #3498db 0%, #9b59b6 100%);
  color: white;
}

.hero-section .btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.8);
  color: white;
  background: transparent;
}

.hero-section .btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #3498db;
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(52, 152, 219, 0.3);
}

/* Process Steps */
.process-step {
  padding: 2rem 1rem;
  transition: all 0.3s ease;
}

.process-step:hover {
  transform: translateY(-5px);
}

.step-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--accent-color)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.step-icon i {
  font-size: 2rem;
  color: white;
}

.step-icon::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--accent-color)
  );
  opacity: 0.2;
  transform: scale(1.2);
  z-index: -1;
}

/* Page Headers */
.page-header {
  background: var(--white-bg);
  border-bottom: 1px solid var(--border-color);
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.page-header h1 {
  margin-bottom: 0.5rem;
}

.page-header .breadcrumb {
  background: transparent;
  padding: 0;
  margin-bottom: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
  color: var(--muted-text);
}

.breadcrumb-item a {
  color: var(--muted-text);
  text-decoration: none;
}

.breadcrumb-item a:hover {
  color: var(--primary-color);
}

.breadcrumb-item.active {
  color: var(--dark-text);
}

/* Tables */
.table {
  background-color: var(--white-bg);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.table thead th {
  background-color: var(--subtle-border);
  color: var(--dark-text);
  border: none;
  font-weight: 600;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
}

.table tbody td {
  padding: 0.75rem 1rem;
  vertical-align: middle;
  border-top: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.table tbody tr:hover {
  background-color: var(--subtle-border);
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-pending {
  background-color: rgba(214, 158, 46, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(214, 158, 46, 0.2);
}

.status-approved {
  background-color: rgba(56, 161, 105, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(56, 161, 105, 0.2);
}

.status-rejected {
  background-color: rgba(229, 62, 62, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(229, 62, 62, 0.2);
}

.status-disbursed {
  background-color: rgba(74, 85, 104, 0.1);
  color: var(--secondary-color);
  border: 1px solid rgba(74, 85, 104, 0.2);
}

/* Spacing Utilities */
.py-section {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.mb-section {
  margin-bottom: 3rem;
}

/* Professional Alerts */
.alert {
  border-radius: 12px;
  border: none;
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid;
}

.alert-success {
  background-color: rgba(56, 161, 105, 0.1);
  color: var(--success-color);
  border-left-color: var(--success-color);
}

.alert-danger {
  background-color: rgba(229, 62, 62, 0.1);
  color: var(--danger-color);
  border-left-color: var(--danger-color);
}

.alert-warning {
  background-color: rgba(214, 158, 46, 0.1);
  color: var(--warning-color);
  border-left-color: var(--warning-color);
}

.alert-info {
  background-color: rgba(74, 85, 104, 0.1);
  color: var(--secondary-color);
  border-left-color: var(--secondary-color);
}

/* Avatar Circle */
.avatar-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--accent-color)
  );
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.8rem;
}

/* File Upload Styling */
.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  background-color: var(--subtle-border);
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(26, 26, 26, 0.02);
}

.file-upload-area.dragover {
  border-color: var(--primary-color);
  background-color: rgba(26, 26, 26, 0.05);
}

/* Responsive Improvements */
@media (max-width: 768px) {
  .container,
  .container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .main-content {
    padding: 1.5rem 0;
  }

  .content-wrapper {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .page-content {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

  .auth-card {
    margin: 1rem;
    padding: 2rem 1.5rem;
  }

  .dashboard-card h3 {
    font-size: 1.5rem;
  }

  .table-responsive {
    font-size: 0.85rem;
  }

  .navbar-nav .nav-link {
    padding: 0.75rem 1rem !important;
  }
}

/* Loading States */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Approval Workflow Visualization */
.approval-flow {
  padding: 1rem 0;
}

.approval-step {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--accent-color)
  );
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-arrow {
  text-align: center;
  margin: 0.5rem 0;
  margin-left: 16px;
}

/* Modal Improvements */
.modal-content {
  border-radius: 12px;
  border: none;
  box-shadow: var(--shadow-large);
}

.modal-header {
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid var(--border-color);
  padding: 1.5rem;
}
