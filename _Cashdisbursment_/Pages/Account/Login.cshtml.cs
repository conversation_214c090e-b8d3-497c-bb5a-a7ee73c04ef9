using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Pages.Account
{
    public class LoginModel : PageModel
    {
        private readonly UserService _userService;

        public LoginModel(UserService userService)
        {
            _userService = userService;
        }

        [BindProperty]
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;

        [BindProperty]
        public bool RememberMe { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        public IActionResult OnGet()
        {
            // Redirect if already authenticated
            if (AuthUtility.IsAuthenticated(HttpContext.Session))
            {
                var user = AuthUtility.GetCurrentUser(HttpContext.Session);
                if (user != null)
                {
                    if (AuthUtility.IsAdmin(HttpContext.Session))
                        return RedirectToPage("/Admin/Dashboard");
                    else if (AuthUtility.IsApprover(HttpContext.Session))
                        return RedirectToPage("/Admin/Dashboard");
                    else
                        return RedirectToPage("/Company/Dashboard");
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                var user = await _userService.ValidateUserAsync(Email, Password);
                
                if (user == null)
                {
                    ErrorMessage = "Invalid email or password.";
                    return Page();
                }

                if (!user.Status)
                {
                    ErrorMessage = "Your account has been deactivated. Please contact the administrator.";
                    return Page();
                }

                // Set session
                AuthUtility.SetCurrentUser(HttpContext.Session, user);

                // Redirect based on role
                if (user.Role == "Admin" || user.Role == "SuperAdmin")
                {
                    return RedirectToPage("/Admin/Dashboard");
                }
                else if (user.Role == "Approver")
                {
                    return RedirectToPage("/Admin/Dashboard");
                }
                else
                {
                    return RedirectToPage("/Company/Dashboard");
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred during login. Please try again.";
                // Log the exception in a real application
                return Page();
            }
        }
    }
}
