using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Company
{
    public class DashboardModel : PageModel
    {
        private readonly ApplicationService _applicationService;

        public DashboardModel(ApplicationService applicationService)
        {
            _applicationService = applicationService;
        }

        public User? CurrentUser { get; set; }
        public int TotalApplications { get; set; }
        public int PendingApplications { get; set; }
        public int ApprovedApplications { get; set; }
        public double TotalDisbursed { get; set; }
        public List<Application> RecentApplications { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsCompanyUser(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            if (CurrentUser.CompanyID.HasValue)
            {
                var applications = await _applicationService.GetApplicationsByCompanyAsync(CurrentUser.CompanyID.Value);
                
                TotalApplications = applications.Count;
                PendingApplications = applications.Count(a => a.Status == "Pending");
                ApprovedApplications = applications.Count(a => a.Status == "Approved");
                TotalDisbursed = applications.Where(a => a.IsDisbursed).Sum(a => a.RequestedCash);
                RecentApplications = applications.Take(5).ToList();
            }

            return Page();
        }
    }
}
