@page
@model _Cashdisbursment_.Pages.Company.Acquittals.CreateModel
@{
    ViewData["Title"] = "Submit Acquittal";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-receipt me-2"></i>Submit Acquittal - Application #@Model.Application?.ApplicationID
                </h1>
                <a asp-page="/Company/Acquittals" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Acquittals
                </a>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (Model.Application != null)
    {
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>Application Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Description:</label>
                                    <p class="mb-0">@Model.Application.Description</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Purpose:</label>
                                    <p class="mb-0">@Model.Application.Purpose</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Disbursed Amount:</label>
                                    <p class="mb-0 text-success fw-bold">$@((Model.Application.DisbursedCash ?? 0).ToString("N2"))</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Date Disbursed:</label>
                                    <p class="mb-0">@Model.Application.DateDisbursed?.ToString("MMM dd, yyyy")</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Total Acquitted:</label>
                                    <p class="mb-0 text-info fw-bold">$@Model.TotalAcquitted.ToString("N2")</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Outstanding Balance:</label>
                                    <p class="mb-0 text-warning fw-bold">$@Model.OutstandingBalance.ToString("N2")</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                @if (Model.ExistingSubmissions.Any())
                {
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>Previous Submissions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Document</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var submission in Model.ExistingSubmissions)
                                        {
                                            <tr>
                                                <td>@submission.Date.ToString("MMM dd, yyyy")</td>
                                                <td>@submission.Description</td>
                                                <td class="fw-bold">$@submission.Amount.ToString("N2")</td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(submission.PDFLocation))
                                                    {
                                                        <a href="@submission.PDFLocation" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-file-pdf"></i>
                                                        </a>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">No document</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                }
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-plus me-2"></i>New Acquittal Submission
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data">
                            <input type="hidden" asp-for="ApplicationId" />
                            
                            <div class="mb-3">
                                <label asp-for="Description" class="form-label">Description *</label>
                                <textarea asp-for="Description" class="form-control" rows="3" 
                                         placeholder="Describe what this acquittal covers..."></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                            </div>

                            <div class="mb-3">
                                <label asp-for="Amount" class="form-label">Amount *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input asp-for="Amount" class="form-control" placeholder="0.00" />
                                </div>
                                <span asp-validation-for="Amount" class="text-danger"></span>
                                <div class="form-text">
                                    Maximum: $@Model.OutstandingBalance.ToString("N2")
                                </div>
                            </div>

                            <div class="mb-3">
                                <label asp-for="SupportingDocument" class="form-label">Supporting Document</label>
                                <input asp-for="SupportingDocument" type="file" class="form-control" accept=".pdf,.jpg,.jpeg,.png" />
                                <span asp-validation-for="SupportingDocument" class="text-danger"></span>
                                <div class="form-text">
                                    Upload receipts, invoices, or other supporting documents (PDF, JPG, PNG)
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Acquittal
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle me-2"></i>Acquittal Guidelines
                        </h6>
                        <ul class="mb-0 small">
                            <li>Provide detailed descriptions of expenses</li>
                            <li>Include supporting documentation when possible</li>
                            <li>Ensure amounts match actual expenditures</li>
                            <li>Submit acquittals promptly after expenses</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
