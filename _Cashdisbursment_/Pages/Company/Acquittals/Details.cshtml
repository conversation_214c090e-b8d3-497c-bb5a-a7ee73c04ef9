@page
@model _Cashdisbursment_.Pages.Company.Acquittals.DetailsModel
@{
    ViewData["Title"] = "Acquittal Details";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-receipt me-2"></i>Acquittal Details - Application #@Model.Acquittal?.Application?.ApplicationID
                </h1>
                <div>
                    @if (Model.OutstandingBalance > 0)
                    {
                        <a asp-page="/Company/Acquittals/Create" asp-route-applicationId="@Model.Acquittal?.Application?.ApplicationID" 
                           class="btn btn-success me-2">
                            <i class="fas fa-plus me-1"></i>Add Submission
                        </a>
                    }
                    <a asp-page="/Company/Acquittals" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Acquittals
                    </a>
                </div>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (Model.Acquittal != null)
    {
        <!-- Application Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>Application Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Description:</label>
                                    <p class="mb-0">@Model.Acquittal.Application?.Description</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Purpose:</label>
                                    <p class="mb-0">@Model.Acquittal.Application?.Purpose</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Disbursed Amount:</label>
                                    <p class="mb-0 text-success fw-bold">$@((Model.Acquittal.Application?.DisbursedCash ?? 0).ToString("N2"))</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Date Disbursed:</label>
                                    <p class="mb-0">@Model.Acquittal.Application?.DateDisbursed?.ToString("MMM dd, yyyy")</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acquittal Summary -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success">Total Disbursed</h5>
                        <h3 class="mb-0">$@((Model.Acquittal.Application?.DisbursedCash ?? 0).ToString("N2"))</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info">Total Acquitted</h5>
                        <h3 class="mb-0">$@Model.TotalAcquitted.ToString("N2")</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title @(Model.OutstandingBalance > 0 ? "text-warning" : "text-success")">Outstanding</h5>
                        <h3 class="mb-0">$@Model.OutstandingBalance.ToString("N2")</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acquittal Submissions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            Acquittal Submissions 
                            <span class="badge bg-secondary">@Model.Submissions.Count</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (Model.Submissions.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Submitted By</th>
                                            <th>Document</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var submission in Model.Submissions.OrderByDescending(s => s.Date))
                                        {
                                            <tr>
                                                <td>
                                                    <div class="fw-bold">@submission.Date.ToString("MMM dd, yyyy")</div>
                                                    <small class="text-muted">@submission.Date.ToString("HH:mm")</small>
                                                </td>
                                                <td>
                                                    <div class="text-wrap" style="max-width: 300px;">
                                                        @submission.Description
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="fw-bold text-success">$@submission.Amount.ToString("N2")</span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">@submission.SubmittedBy</small>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(submission.PDFLocation))
                                                    {
                                                        <a href="@submission.PDFLocation" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-file-pdf me-1"></i>View
                                                        </a>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">No document</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            <!-- Summary Row -->
                            <div class="border-top pt-3 mt-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Total Submissions: @Model.Submissions.Count</strong>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <strong>Total Amount: $@Model.TotalAcquitted.ToString("N2")</strong>
                                    </div>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Submissions Yet</h5>
                                <p class="text-muted">No acquittal submissions have been made for this application.</p>
                                @if (Model.OutstandingBalance > 0)
                                {
                                    <a asp-page="/Company/Acquittals/Create" asp-route-applicationId="@Model.Acquittal.Application?.ApplicationID" 
                                       class="btn btn-success">
                                        <i class="fas fa-plus me-2"></i>Submit First Acquittal
                                    </a>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        @if (Model.OutstandingBalance <= 0 && Model.Submissions.Any())
        {
            <div class="alert alert-success mt-4" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Acquittal Complete!</strong> All disbursed funds have been properly acquitted.
            </div>
        }
    }
</div>
