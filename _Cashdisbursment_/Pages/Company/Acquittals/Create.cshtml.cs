using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Pages.Company.Acquittals
{
    public class CreateModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly AcquittalService _acquittalService;
        private readonly IWebHostEnvironment _environment;

        public CreateModel(
            ApplicationService applicationService,
            AcquittalService acquittalService,
            IWebHostEnvironment environment)
        {
            _applicationService = applicationService;
            _acquittalService = acquittalService;
            _environment = environment;
        }

        public User? CurrentUser { get; set; }
        public Application? Application { get; set; }
        public List<AcquittalSubmission> ExistingSubmissions { get; set; } = new();
        public double TotalAcquitted { get; set; }
        public double OutstandingBalance { get; set; }

        [BindProperty]
        public int ApplicationId { get; set; }

        [BindProperty]
        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public double Amount { get; set; }

        [BindProperty]
        public IFormFile? SupportingDocument { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync(int applicationId)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.IsCompanyUser(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            Application = await _applicationService.GetApplicationByIdAsync(applicationId);

            if (Application == null || Application.CompanyID != CurrentUser.CompanyID)
            {
                ErrorMessage = "Application not found or access denied.";
                return RedirectToPage("/Company/Acquittals");
            }

            if (!Application.IsDisbursed)
            {
                ErrorMessage = "This application has not been disbursed yet.";
                return RedirectToPage("/Company/Applications");
            }

            ApplicationId = applicationId;

            // Check if acquittal exists, create if not
            var hasAcquittal = await _acquittalService.HasAcquittalAsync(applicationId);
            if (!hasAcquittal)
            {
                await _acquittalService.CreateAcquittalAsync(applicationId);
            }

            // Get existing submissions
            var acquittals = await _acquittalService.GetAcquittalsByApplicationIdAsync(applicationId);
            if (acquittals.Any())
            {
                var acquittal = acquittals.First();
                ExistingSubmissions = await _acquittalService.GetSubmissionsByAcquittalIdAsync(acquittal.AcquittalID);
            }

            // Calculate totals
            TotalAcquitted = ExistingSubmissions.Sum(s => s.Amount);
            OutstandingBalance = (Application.DisbursedCash ?? 0) - TotalAcquitted;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.IsCompanyUser(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            if (!ModelState.IsValid)
            {
                await LoadPageDataAsync();
                return Page();
            }

            try
            {
                Application = await _applicationService.GetApplicationByIdAsync(ApplicationId);

                if (Application == null || Application.CompanyID != CurrentUser.CompanyID)
                {
                    ErrorMessage = "Application not found or access denied.";
                    return RedirectToPage("/Company/Acquittals");
                }

                if (!Application.IsDisbursed)
                {
                    ErrorMessage = "This application has not been disbursed yet.";
                    return RedirectToPage("/Company/Applications");
                }

                // Get existing submissions to calculate outstanding balance
                var acquittals = await _acquittalService.GetAcquittalsByApplicationIdAsync(ApplicationId);
                if (!acquittals.Any())
                {
                    ErrorMessage = "Acquittal record not found.";
                    await LoadPageDataAsync();
                    return Page();
                }

                var acquittal = acquittals.First();
                var existingSubmissions = await _acquittalService.GetSubmissionsByAcquittalIdAsync(acquittal.AcquittalID);
                var totalAcquitted = existingSubmissions.Sum(s => s.Amount);
                var outstandingBalance = (Application.DisbursedCash ?? 0) - totalAcquitted;

                // Validate amount doesn't exceed outstanding balance
                if (Amount > outstandingBalance)
                {
                    ModelState.AddModelError(nameof(Amount), $"Amount cannot exceed outstanding balance of ${outstandingBalance:N2}");
                    await LoadPageDataAsync();
                    return Page();
                }

                // Handle file upload
                string? documentPath = null;
                if (SupportingDocument != null)
                {
                    documentPath = await SaveDocumentAsync(SupportingDocument, ApplicationId);
                }

                // Create acquittal submission
                var submission = new AcquittalSubmission
                {
                    AcquittalID = acquittal.AcquittalID,
                    Description = Description,
                    Amount = Amount,
                    PDFLocation = documentPath,
                    Date = DateTime.Now,
                    SubmittedBy = CurrentUser.Email
                };

                var submissionId = await _acquittalService.CreateSubmissionAsync(submission);

                if (submissionId > 0)
                {
                    TempData["SuccessMessage"] = $"Acquittal submission of ${Amount:N2} has been successfully recorded.";
                    return RedirectToPage("/Company/Acquittals");
                }
                else
                {
                    ErrorMessage = "Failed to submit acquittal. Please try again.";
                    await LoadPageDataAsync();
                    return Page();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while submitting the acquittal. Please try again.";
                await LoadPageDataAsync();
                return Page();
            }
        }

        private async Task LoadPageDataAsync()
        {
            Application = await _applicationService.GetApplicationByIdAsync(ApplicationId);
            
            if (Application != null)
            {
                var acquittals = await _acquittalService.GetAcquittalsByApplicationIdAsync(ApplicationId);
                if (acquittals.Any())
                {
                    var acquittal = acquittals.First();
                    ExistingSubmissions = await _acquittalService.GetSubmissionsByAcquittalIdAsync(acquittal.AcquittalID);
                }

                TotalAcquitted = ExistingSubmissions.Sum(s => s.Amount);
                OutstandingBalance = (Application.DisbursedCash ?? 0) - TotalAcquitted;
            }
        }

        private async Task<string?> SaveDocumentAsync(IFormFile file, int applicationId)
        {
            try
            {
                var uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads", "acquittals");
                Directory.CreateDirectory(uploadsFolder);

                var fileName = $"acquittal_{applicationId}_{DateTime.Now:yyyyMMdd_HHmmss}_{file.FileName}";
                var filePath = Path.Combine(uploadsFolder, fileName);

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                return $"/uploads/acquittals/{fileName}";
            }
            catch
            {
                return null;
            }
        }
    }
}
