@page
@model _Cashdisbursment_.Pages.Company.AcquittalsModel
@{
    ViewData["Title"] = "My Acquittals";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-receipt me-2"></i>My Acquittals
                </h1>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary">Total Acquittals</h5>
                    <h3 class="mb-0">@Model.Acquittals.Count</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">Total Disbursed</h5>
                    <h3 class="mb-0">$@Model.TotalDisbursed.ToString("N2")</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info">Total Acquitted</h5>
                    <h3 class="mb-0">$@Model.TotalAcquitted.ToString("N2")</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">Outstanding</h5>
                    <h3 class="mb-0">$@Model.Outstanding.ToString("N2")</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Acquittals Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        Acquittals 
                        <span class="badge bg-secondary">@Model.Acquittals.Count</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Acquittals.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Application ID</th>
                                        <th>Description</th>
                                        <th>Disbursed Amount</th>
                                        <th>Acquitted Amount</th>
                                        <th>Outstanding</th>
                                        <th>Date Disbursed</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var acquittal in Model.Acquittals)
                                    {
                                        var acquittedAmount = Model.AcquittalSubmissions
                                            .Where(s => s.AcquittalID == acquittal.AcquittalID)
                                            .Sum(s => s.Amount);
                                        var outstanding = (acquittal.Application?.DisbursedCash ?? 0) - acquittedAmount;
                                        
                                        <tr>
                                            <td><strong>#@acquittal.Application?.ApplicationID</strong></td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 200px;" title="@acquittal.Application?.Description">
                                                    @acquittal.Application?.Description
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-success">$@((acquittal.Application?.DisbursedCash ?? 0).ToString("N2"))</span>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-info">$@acquittedAmount.ToString("N2")</span>
                                            </td>
                                            <td>
                                                @if (outstanding > 0)
                                                {
                                                    <span class="fw-bold text-warning">$@outstanding.ToString("N2")</span>
                                                }
                                                else
                                                {
                                                    <span class="fw-bold text-success">$0.00</span>
                                                }
                                            </td>
                                            <td>@acquittal.Application?.DateDisbursed?.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="/Company/Acquittals/Details" asp-route-id="@acquittal.AcquittalID" 
                                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if (outstanding > 0)
                                                    {
                                                        <a asp-page="/Company/Acquittals/Create" asp-route-applicationId="@acquittal.Application?.ApplicationID" 
                                                           class="btn btn-sm btn-outline-success" title="Submit Acquittal">
                                                            <i class="fas fa-plus"></i>
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Acquittals Found</h5>
                            <p class="text-muted">You don't have any disbursed applications that require acquittal yet.</p>
                            <a asp-page="/Company/Applications" class="btn btn-primary">
                                <i class="fas fa-file-alt me-2"></i>View Applications
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .status-approved {
        color: #28a745;
        font-weight: 500;
    }
    
    .status-pending {
        color: #ffc107;
        font-weight: 500;
    }
    
    .status-rejected {
        color: #dc3545;
        font-weight: 500;
    }
    
    .status-disbursed {
        color: #17a2b8;
        font-weight: 500;
    }
</style>
