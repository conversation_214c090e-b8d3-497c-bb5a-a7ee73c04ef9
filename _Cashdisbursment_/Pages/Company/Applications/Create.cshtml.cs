using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Pages.Company.Applications
{
    public class CreateModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly ApprovalService _approvalService;
        private readonly EmailService _emailService;
        private readonly IWebHostEnvironment _environment;

        public CreateModel(ApplicationService applicationService, ApprovalService approvalService, 
                          EmailService emailService, IWebHostEnvironment environment)
        {
            _applicationService = applicationService;
            _approvalService = approvalService;
            _emailService = emailService;
            _environment = environment;
        }

        public User? CurrentUser { get; set; }

        [BindProperty]
        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(500)]
        public string Purpose { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Requested cash must be greater than 0")]
        public double RequestedCash { get; set; }

        [BindProperty]
        public IFormFile? SupportingDocument { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsCompanyUser(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsCompanyUser(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                string? pdfLocation = null;

                // Handle file upload
                if (SupportingDocument != null)
                {
                    // Validate file
                    var allowedExtensions = new[] { ".pdf", ".doc", ".docx" };
                    var fileExtension = Path.GetExtension(SupportingDocument.FileName).ToLowerInvariant();
                    
                    if (!allowedExtensions.Contains(fileExtension))
                    {
                        ErrorMessage = "Only PDF, DOC, and DOCX files are allowed.";
                        return Page();
                    }

                    if (SupportingDocument.Length > 10 * 1024 * 1024) // 10MB limit
                    {
                        ErrorMessage = "File size cannot exceed 10MB.";
                        return Page();
                    }

                    // Create uploads directory if it doesn't exist
                    var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads", "applications");
                    Directory.CreateDirectory(uploadsPath);

                    // Generate unique filename
                    var fileName = $"{Guid.NewGuid()}{fileExtension}";
                    var filePath = Path.Combine(uploadsPath, fileName);

                    // Save file
                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await SupportingDocument.CopyToAsync(stream);
                    }

                    pdfLocation = $"/uploads/applications/{fileName}";
                }

                // Create application
                var application = new Application
                {
                    CompanyID = CurrentUser.CompanyID!.Value,
                    Description = Description,
                    Purpose = Purpose,
                    RequestedCash = RequestedCash,
                    DateRequested = DateTime.Now,
                    Status = "Pending",
                    IsDisbursed = false,
                    ApprovalLevel = 0,
                    PDFLocation = pdfLocation
                };

                var applicationId = await _applicationService.CreateApplicationAsync(application);

                if (applicationId > 0)
                {
                    // Send notification to first approver
                    var firstApprover = await _approvalService.GetNextApproverAsync(0);
                    if (firstApprover != null)
                    {
                        await _emailService.SendApprovalRequestEmailAsync(
                            firstApprover.Email,
                            applicationId,
                            CurrentUser.Company?.Name ?? "Unknown Company",
                            RequestedCash,
                            Description
                        );
                    }

                    TempData["SuccessMessage"] = "Application submitted successfully! You will be notified of any status updates.";
                    return RedirectToPage("/Company/Applications");
                }
                else
                {
                    ErrorMessage = "Failed to submit application. Please try again.";
                    return Page();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while submitting the application. Please try again.";
                // Log the exception in a real application
                return Page();
            }
        }
    }
}
