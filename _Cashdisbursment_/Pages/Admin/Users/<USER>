@page
@model _Cashdisbursment_.Pages.Admin.Users.CreateModel
@{
    ViewData["Title"] = "Create User";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-page="/Admin/Dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a asp-page="/Admin/Users">Users</a></li>
                    <li class="breadcrumb-item active">Create User</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-user-plus me-2"></i>Create New User
                </h1>
                <a asp-page="/Admin/Users" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Users
                </a>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="py-section">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">User Information</h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <div class="mb-3">
                                <label asp-for="Email" class="form-label"></label>
                                <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                                <div class="form-text">This will be the user's login email address.</div>
                            </div>

                            <div class="mb-3">
                                <label asp-for="Role" class="form-label"></label>
                                <select asp-for="Role" class="form-select">
                                    <option value="Admin">Admin</option>
                                    <option value="Approver">Approver</option>
                                </select>
                                <span asp-validation-for="Role" class="text-danger"></span>
                                <div class="form-text">
                                    <strong>Admin:</strong> Can manage companies, users, and applications.<br>
                                    <strong>Approver:</strong> Can review and approve/reject applications.
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check">
                                    <input asp-for="SendCredentials" class="form-check-input" type="checkbox" />
                                    <label asp-for="SendCredentials" class="form-check-label">
                                        Send login credentials via email
                                    </label>
                                </div>
                                <div class="form-text">
                                    If checked, the user will receive an email with their login credentials.
                                    A random password will be generated automatically.
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a asp-page="/Admin/Users" class="btn btn-outline-secondary me-md-2">Cancel</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Create User
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Information Card -->
                
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
