@page "{id:int}"
@model _Cashdisbursment_.Pages.Admin.Applications.ReviewModel
@{
    ViewData["Title"] = $"Review Application #{Model.Application?.ApplicationID}";
}

<div class="container">
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-page="/Admin/Dashboard">Dashboard</a></li>
                <li class="breadcrumb-item"><a asp-page="/Admin/Applications">Applications</a></li>
                <li class="breadcrumb-item active">Review Application</li>
            </ol>
        </nav>
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-2">Review Application #@Model.Application?.ApplicationID</h1>
                <p class="text-muted mb-0">@Model.Application?.Company?.Name</p>
            </div>
            <div class="col-auto">
                <a asp-page="/Admin/Applications" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Applications
                </a>
            </div>
        </div>
    </div>

    @if (Model.Application == null)
    {
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>Application not found.
        </div>
        return;
    }

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="py-section">
        <div class="row">
            <div class="col-lg-8">
                <!-- Application Details -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Application Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Requested Amount</label>
                                <div class="h4 text-primary">$@Model.Application.RequestedCash.ToString("N2")</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date Requested</label>
                                <div>@Model.Application.DateRequested.ToString("MMMM dd, yyyy 'at' hh:mm tt")</div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label fw-bold">Description</label>
                            <div class="border rounded p-3 bg-light">
                                @Model.Application.Description
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label fw-bold">Purpose</label>
                            <div class="border rounded p-3 bg-light">
                                @Model.Application.Purpose
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(Model.Application.PDFLocation))
                        {
                            <div class="mb-4">
                                <label class="form-label fw-bold">Supporting Document</label>
                                <div>
                                    <a href="@Model.Application.PDFLocation" target="_blank" class="btn btn-outline-primary">
                                        <i class="fas fa-file-pdf me-1"></i>View Document
                                    </a>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <!-- Review Form -->
                @if (Model.Application.Status == "Pending" && !Model.Application.IsDisbursed)
                {
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Review Decision</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                <input type="hidden" name="ApplicationId" value="@Model.Application.ApplicationID" />
                                
                                <div class="mb-4">
                                    <label class="form-label fw-bold">Decision</label>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="Decision" value="approve" id="approve" required>
                                                <label class="form-check-label text-success fw-bold" for="approve">
                                                    <i class="fas fa-check-circle me-1"></i>Approve Application
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="Decision" value="reject" id="reject" required>
                                                <label class="form-check-label text-danger fw-bold" for="reject">
                                                    <i class="fas fa-times-circle me-1"></i>Reject Application
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="Comment" class="form-label fw-bold">Comments</label>
                                    <textarea name="Comment" id="Comment" class="form-control" rows="4" 
                                              placeholder="Add your review comments here..."></textarea>
                                    <div class="form-text">Provide feedback or reasons for your decision.</div>
                                </div>

                                <div class="d-flex justify-content-end gap-3">
                                    <a asp-page="/Admin/Applications" class="btn btn-outline-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-gavel me-1"></i>Submit Review
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                }
                else
                {
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Review Status</h5>
                        </div>
                        <div class="card-body">
                            @if (Model.Application.IsDisbursed)
                            {
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    This application has been disbursed and cannot be modified.
                                </div>
                            }
                            else if (Model.Application.Status == "Approved")
                            {
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    This application has been approved and is ready for disbursement.
                                </div>
                            }
                            else if (Model.Application.Status == "Rejected")
                            {
                                <div class="alert alert-danger">
                                    <i class="fas fa-times-circle me-2"></i>
                                    This application has been rejected.
                                </div>
                            }
                            
                            @if (!string.IsNullOrEmpty(Model.Application.ApprovalComment))
                            {
                                <div class="mt-3">
                                    <label class="form-label fw-bold">Review Comments</label>
                                    <div class="border rounded p-3 bg-light">
                                        @Model.Application.ApprovalComment
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>

            <div class="col-lg-4">
                <!-- Status Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Application Status</h6>
                    </div>
                    <div class="card-body text-center">
                        @if (Model.Application.IsDisbursed)
                        {
                            <div class="mb-3">
                                <i class="fas fa-money-bill-wave fa-3x text-success"></i>
                            </div>
                            <h5 class="text-success">Disbursed</h5>
                            <p class="text-muted">Funds have been disbursed</p>
                        }
                        else if (Model.Application.Status == "Approved")
                        {
                            <div class="mb-3">
                                <i class="fas fa-check-circle fa-3x text-success"></i>
                            </div>
                            <h5 class="text-success">Approved</h5>
                            <p class="text-muted">Ready for disbursement</p>
                        }
                        else if (Model.Application.Status == "Rejected")
                        {
                            <div class="mb-3">
                                <i class="fas fa-times-circle fa-3x text-danger"></i>
                            </div>
                            <h5 class="text-danger">Rejected</h5>
                            <p class="text-muted">Application was rejected</p>
                        }
                        else
                        {
                            <div class="mb-3">
                                <i class="fas fa-clock fa-3x text-warning"></i>
                            </div>
                            <h5 class="text-warning">Pending Review</h5>
                            <p class="text-muted">Awaiting approval decision</p>
                        }
                    </div>
                </div>

                <!-- Approval Progress -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Approval Progress</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>Current Level</span>
                                <span class="badge bg-info">@Model.Application.ApprovalLevel / @Model.MaxApprovalLevel</span>
                            </div>
                            <div class="progress mt-2">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: @(Model.MaxApprovalLevel > 0 ? (Model.Application.ApprovalLevel * 100.0 / Model.MaxApprovalLevel) : 0)%">
                                </div>
                            </div>
                        </div>
                        
                        @if (Model.Application.ApprovalLevel == 0)
                        {
                            <small class="text-muted">Review process not yet started</small>
                        }
                        else if (Model.Application.Status == "Approved")
                        {
                            <small class="text-success">All required approvals completed</small>
                        }
                        else if (Model.Application.Status == "Rejected")
                        {
                            <small class="text-danger">Application was rejected</small>
                        }
                        else
                        {
                            <small class="text-info">Currently at approval level @Model.Application.ApprovalLevel</small>
                        }
                    </div>
                </div>

                <!-- Company Information -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Company Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Company Name</label>
                            <div>@Model.Application.Company?.Name</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Application ID</label>
                            <div>#@Model.Application.ApplicationID</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Requested Amount</label>
                            <div class="h5 text-primary">$@Model.Application.RequestedCash.ToString("N2")</div>
                        </div>
                        <div>
                            <label class="form-label fw-bold">Days Since Request</label>
                            <div>@((DateTime.Now - Model.Application.DateRequested).Days) days</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
