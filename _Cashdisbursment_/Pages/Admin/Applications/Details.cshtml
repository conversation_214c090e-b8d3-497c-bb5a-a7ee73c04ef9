@page "{id:int}"
@model _Cashdisbursment_.Pages.Admin.Applications.DetailsModel
@{
    ViewData["Title"] = $"Application #{Model.Application?.ApplicationID} Details";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-page="/Admin/Dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a asp-page="/Admin/Applications">Applications</a></li>
                    <li class="breadcrumb-item active">Application #@Model.Application?.ApplicationID</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-file-alt me-2"></i>Application #@Model.Application?.ApplicationID Details
                </h1>
                <div>
                    @if (Model.Application?.Status == "Pending" && !Model.Application.IsDisbursed)
                    {
                        <a asp-page="/Admin/Applications/Review" asp-route-id="@Model.Application.ApplicationID"
                           class="btn btn-warning me-2">
                            <i class="fas fa-gavel me-1"></i>Review Application
                        </a>
                    }
                    @if (Model.Application?.Status == "Approved" && !Model.Application.IsDisbursed)
                    {
                        <a asp-page="/Admin/Applications/Disburse" asp-route-id="@Model.Application.ApplicationID" 
                           class="btn btn-success me-2">
                            <i class="fas fa-money-bill-wave me-1"></i>Disburse Funds
                        </a>
                    }
                    <a asp-page="/Admin/Applications" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Applications
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="py-section">
        <div class="row">
            <div class="col-lg-8">
                <!-- Application Details -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Application Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Requested Amount</label>
                                <div class="h4 text-primary">$@Model.Application.RequestedCash.ToString("N2")</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date Requested</label>
                                <div>@Model.Application.DateRequested.ToString("MMMM dd, yyyy 'at' hh:mm tt")</div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Status</label>
                                <div>
                                    @if (Model.Application.Status == "Pending")
                                    {
                                        <span class="status-indicator status-pending">Pending Review</span>
                                    }
                                    else if (Model.Application.Status == "Approved")
                                    {
                                        <span class="status-indicator status-approved">Approved</span>
                                    }
                                    else if (Model.Application.Status == "Rejected")
                                    {
                                        <span class="status-indicator status-rejected">Rejected</span>
                                    }
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Approval Level</label>
                                <div>
                                    @if (Model.Application.ApprovalLevel > 0)
                                    {
                                        <span class="badge bg-primary">Level @Model.Application.ApprovalLevel</span>
                                        @if (Model.MaxApprovalLevel > 0)
                                        {
                                            <span class="text-muted">of @Model.MaxApprovalLevel</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="text-muted">Not yet reviewed</span>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label fw-bold">Description</label>
                            <div class="border rounded p-3 bg-light">
                                @Model.Application.Description
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label fw-bold">Purpose</label>
                            <div class="border rounded p-3 bg-light">
                                @Model.Application.Purpose
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(Model.Application.ApprovalComment))
                        {
                            <div class="mb-4">
                                <label class="form-label fw-bold">Approval Comments</label>
                                <div class="border rounded p-3 bg-light">
                                    @Model.Application.ApprovalComment
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(Model.Application.PDFLocation))
                        {
                            <div class="mb-3">
                                <label class="form-label fw-bold">Supporting Document</label>
                                <div>
                                    <a href="@Model.Application.PDFLocation" target="_blank" class="btn btn-outline-primary">
                                        <i class="fas fa-file-pdf me-1"></i>View Document
                                    </a>
                                </div>
                            </div>
                        }

                        @if (Model.Application.IsDisbursed)
                        {
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Disbursed</strong> on @Model.Application.DateDisbursed?.ToString("MMMM dd, yyyy 'at' hh:mm tt")
                                @if (!string.IsNullOrEmpty(Model.Application.DisbursedBy))
                                {
                                    <span>by @Model.Application.DisbursedBy</span>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Company Information -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Company Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Company Name</label>
                            <div>@Model.Application.Company?.Name</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Application ID</label>
                            <div>#@Model.Application.ApplicationID</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Requested Amount</label>
                            <div class="h5 text-primary">$@Model.Application.RequestedCash.ToString("N2")</div>
                        </div>
                        <div>
                            <label class="form-label fw-bold">Days Since Request</label>
                            <div>@((DateTime.Now - Model.Application.DateRequested).Days) days</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
