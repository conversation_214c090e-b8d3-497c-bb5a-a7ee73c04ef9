using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin.Applications
{
    public class ReviewModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly ApprovalService _approvalService;
        private readonly EmailService _emailService;
        private readonly CompanyService _companyService;

        public ReviewModel(ApplicationService applicationService, ApprovalService approvalService, 
                          EmailService emailService, CompanyService companyService)
        {
            _applicationService = applicationService;
            _approvalService = approvalService;
            _emailService = emailService;
            _companyService = companyService;
        }

        public User? CurrentUser { get; set; }
        public Application? Application { get; set; }
        public int MaxApprovalLevel { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.HasApprovalAccess(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            Application = await _applicationService.GetApplicationByIdAsync(id);
            
            if (Application == null)
            {
                return NotFound();
            }

            MaxApprovalLevel = await _approvalService.GetMaxApprovalLevelAsync();

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int applicationId, string decision, string? comment)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.HasApprovalAccess(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                Application = await _applicationService.GetApplicationByIdAsync(applicationId);
                
                if (Application == null)
                {
                    ErrorMessage = "Application not found.";
                    if (AuthUtility.IsApprover(HttpContext.Session))
                    {
                        return RedirectToPage("/Approver/Applications");
                    }
                    else
                    {
                        return RedirectToPage("/Admin/Applications");
                    }
                }

                if (Application.Status == "Approved" || Application.Status == "Rejected" || Application.IsDisbursed)
                {
                    ErrorMessage = "This application has already been processed.";
                    return RedirectToPage(new { id = applicationId });
                }

                bool isApproved = decision.ToLower() == "approve";
                int newApprovalLevel = Application.ApprovalLevel + 1;
                
                if (isApproved)
                {
                    // Check if this is the final approval level
                    var maxLevel = await _approvalService.GetMaxApprovalLevelAsync();
                    bool isFinalApproval = newApprovalLevel >= maxLevel;

                    // Update application approval
                    string status = isFinalApproval ? "Approved" : "Pending";
                    await _applicationService.UpdateApplicationApprovalAsync(
                        applicationId, newApprovalLevel, status, comment);

                    if (isFinalApproval)
                    {
                        // Final approval - application is fully approved
                        SuccessMessage = "Application has been fully approved and is ready for disbursement.";
                        
                        // Send approval notification to company
                        var company = await _companyService.GetCompanyByIdAsync(Application.CompanyID);
                        if (company != null)
                        {
                            await _emailService.SendApplicationStatusEmailAsync(
                                company.Email, company.Name, applicationId, "Approved", comment);
                        }
                    }
                    else
                    {
                        // Partial approval - send to next approver
                        var nextApprover = await _approvalService.GetNextApproverAsync(newApprovalLevel - 1);
                        if (nextApprover != null)
                        {
                            await _emailService.SendApprovalRequestEmailAsync(
                                nextApprover.Email, applicationId, Application.Company?.Name ?? "Unknown",
                                Application.RequestedCash, Application.Description);
                        }
                        
                        SuccessMessage = $"Application approved at level {newApprovalLevel}. Sent to next approver.";
                    }
                }
                else
                {
                    // Rejection - mark as rejected
                    await _applicationService.UpdateApplicationApprovalAsync(
                        applicationId, newApprovalLevel, "Rejected", comment);

                    SuccessMessage = "Application has been rejected.";
                    
                    // Send rejection notification to company
                    var company = await _companyService.GetCompanyByIdAsync(Application.CompanyID);
                    if (company != null)
                    {
                        await _emailService.SendApplicationStatusEmailAsync(
                            company.Email, company.Name, applicationId, "Rejected", comment);
                    }
                }

                // Redirect based on user role
                if (AuthUtility.IsApprover(HttpContext.Session))
                {
                    return RedirectToPage("/Approver/Applications");
                }
                else
                {
                    return RedirectToPage("/Admin/Applications");
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while processing the review. Please try again.";
                // Log the exception in a real application
                return RedirectToPage(new { id = applicationId });
            }
        }
    }
}
