using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin.Acquittals
{
    public class DetailsModel : PageModel
    {
        private readonly AcquittalService _acquittalService;

        public DetailsModel(AcquittalService acquittalService)
        {
            _acquittalService = acquittalService;
        }

        public User? CurrentUser { get; set; }
        public Acquittal? Acquittal { get; set; }
        public List<AcquittalSubmission> Submissions { get; set; } = new();
        public double TotalAcquitted { get; set; }
        public double OutstandingBalance { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                Acquittal = await _acquittalService.GetAcquittalByIdAsync(id);

                if (Acquittal == null)
                {
                    ErrorMessage = "Acquittal not found.";
                    return RedirectToPage("/Admin/Acquittals");
                }

                // Get all submissions for this acquittal
                Submissions = await _acquittalService.GetSubmissionsByAcquittalIdAsync(id);

                // Calculate totals
                TotalAcquitted = Submissions.Sum(s => s.Amount);
                OutstandingBalance = (Acquittal.Application?.DisbursedCash ?? 0) - TotalAcquitted;

                return Page();
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while loading acquittal details. Please try again.";
                return RedirectToPage("/Admin/Acquittals");
            }
        }
    }
}
