using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin
{
    public class UsersModel : PageModel
    {
        private readonly UserService _userService;

        public UsersModel(UserService userService)
        {
            _userService = userService;
        }

        public User? CurrentUser { get; set; }
        public List<User> Users { get; set; } = new();

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            Users = await _userService.GetAllUsersAsync();

            return Page();
        }

        public async Task<IActionResult> OnPostToggleStatusAsync(int userId)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                var user = await _userService.GetUserByIdAsync(userId);
                if (user == null)
                {
                    ErrorMessage = "User not found.";
                    return RedirectToPage();
                }

                // Toggle status
                user.Status = !user.Status;
                var success = await _userService.UpdateUserAsync(user);

                if (success)
                {
                    SuccessMessage = $"User {user.Email} has been {(user.Status ? "activated" : "deactivated")}.";
                }
                else
                {
                    ErrorMessage = "Failed to update user status.";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while updating user status.";
            }

            return RedirectToPage();
        }
    }
}
