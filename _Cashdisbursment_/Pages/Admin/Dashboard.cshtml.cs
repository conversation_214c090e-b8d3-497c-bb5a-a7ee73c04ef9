using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin
{
    public class DashboardModel : PageModel
    {
        private readonly CompanyService _companyService;
        private readonly ApplicationService _applicationService;

        public DashboardModel(CompanyService companyService, ApplicationService applicationService)
        {
            _companyService = companyService;
            _applicationService = applicationService;
        }

        public User? CurrentUser { get; set; }
        
        // Statistics
        public int TotalCompanies { get; set; }
        public int ActiveCompanies { get; set; }
        public int TotalApplications { get; set; }
        public int PendingApplications { get; set; }
        public int ApprovedApplications { get; set; }
        public int DisbursedApplications { get; set; }
        public double TotalDisbursed { get; set; }
        public double MonthlyDisbursed { get; set; }

        // Recent data
        public List<Application> RecentApplications { get; set; } = new();
        public List<Application> RecentPendingApplications { get; set; } = new();
        public List<Application> ReadyForDisbursement { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            await LoadDashboardDataAsync();

            return Page();
        }

        private async Task LoadDashboardDataAsync()
        {
            // Load companies data
            var companies = await _companyService.GetAllCompaniesAsync();
            TotalCompanies = companies.Count;
            ActiveCompanies = companies.Count(c => c.Status);

            // Load applications data
            var applications = await _applicationService.GetAllApplicationsAsync();
            TotalApplications = applications.Count;
            PendingApplications = applications.Count(a => a.Status == "Pending");
            ApprovedApplications = applications.Count(a => a.Status == "Approved");
            DisbursedApplications = applications.Count(a => a.IsDisbursed);

            // Calculate disbursement amounts
            TotalDisbursed = applications.Where(a => a.IsDisbursed).Sum(a => a.RequestedCash);
            
            var currentMonth = DateTime.Now.Month;
            var currentYear = DateTime.Now.Year;
            MonthlyDisbursed = applications
                .Where(a => a.IsDisbursed && 
                           a.DateDisbursed.HasValue && 
                           a.DateDisbursed.Value.Month == currentMonth && 
                           a.DateDisbursed.Value.Year == currentYear)
                .Sum(a => a.RequestedCash);

            // Load recent data
            RecentApplications = applications
                .OrderByDescending(a => a.DateRequested)
                .ToList();

            RecentPendingApplications = await _applicationService.GetPendingApplicationsAsync();
            ReadyForDisbursement = await _applicationService.GetApprovedNotDisbursedApplicationsAsync();
        }
    }
}
