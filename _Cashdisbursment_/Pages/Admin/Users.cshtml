@page
@model _Cashdisbursment_.Pages.Admin.UsersModel
@{
    ViewData["Title"] = "Manage Users";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-users me-2"></i>Manage Users
                </h1>
                <a asp-page="/Admin/Users/<USER>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add User
                </a>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="py-section">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">All Users</h5>
            </div>
            <div class="card-body">
                @if (Model.Users.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Role</th>
                                    <th>Company</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in Model.Users.OrderBy(u => u.Role).ThenBy(u => u.Email))
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-2">
                                                    @user.Email.Substring(0, 1).ToUpper()
                                                </div>
                                                <div>
                                                    <div class="fw-medium">@user.Email</div>
                                                    <small class="text-muted">ID: @user.UserID</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if (user.Role == "Admin")
                                            {
                                                <span class="badge bg-danger">Admin</span>
                                            }
                                            else if (user.Role == "SuperAdmin")
                                            {
                                                <span class="badge bg-dark">Super Admin</span>
                                            }
                                            else if (user.Role == "Approver")
                                            {
                                                <span class="badge bg-warning">Approver</span>
                                            }
                                            else if (user.Role == "Company")
                                            {
                                                <span class="badge bg-primary">Company</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">@user.Role</span>
                                            }
                                        </td>
                                        <td>
                                            @if (user.Company != null)
                                            {
                                                <span>@user.Company.Name</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">N/A</span>
                                            }
                                        </td>
                                        <td>
                                            @if (user.Status)
                                            {
                                                <span class="status-indicator status-approved">Active</span>
                                            }
                                            else
                                            {
                                                <span class="status-indicator status-rejected">Inactive</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if (user.UserID != Model.CurrentUser?.UserID)
                                                {
                                                    <form method="post" asp-page-handler="ToggleStatus" class="d-inline">
                                                        <input type="hidden" name="userId" value="@user.UserID" />
                                                        <button type="submit" class="btn btn-sm @(user.Status ? "btn-outline-danger" : "btn-outline-success")" 
                                                                onclick="return confirm('Are you sure you want to @(user.Status ? "deactivate" : "activate") this user?')">
                                                            <i class="fas @(user.Status ? "fa-ban" : "fa-check") me-1"></i>@(user.Status ? "Deactivate" : "Activate")
                                                        </button>
                                                    </form>
                                                }
                                                else
                                                {
                                                    <span class="text-muted small">Current User</span>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-4x text-muted mb-3"></i>
                        <h6 class="text-muted">No users found</h6>
                        <p class="text-muted mb-3">Start by adding your first user.</p>
                        <a asp-page="/Admin/Users/<USER>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add First User
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
