using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin
{
    public class ApplicationsModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly CompanyService _companyService;

        public ApplicationsModel(ApplicationService applicationService, CompanyService companyService)
        {
            _applicationService = applicationService;
            _companyService = companyService;
        }

        public User? CurrentUser { get; set; }
        public List<Application> Applications { get; set; } = new();
        public List<Models.Company> Companies { get; set; } = new();

        // Filter properties
        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? CompanyFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? DateRange { get; set; }

        // Statistics
        public int TotalApplications { get; set; }
        public int PendingApplications { get; set; }
        public int ApprovedApplications { get; set; }
        public double TotalAmount { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            // Load companies for filter dropdown
            Companies = await _companyService.GetAllCompaniesAsync();

            // Load all applications
            var allApplications = await _applicationService.GetAllApplicationsAsync();
            
            // Apply filters
            Applications = FilterApplications(allApplications);

            // Calculate statistics
            CalculateStatistics(allApplications);

            return Page();
        }

        private List<Application> FilterApplications(List<Application> applications)
        {
            var filtered = applications.AsEnumerable();

            // Search filter
            if (!string.IsNullOrEmpty(SearchTerm))
            {
                filtered = filtered.Where(a => 
                    a.Description.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    a.Purpose.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    (a.Company?.Name.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ?? false));
            }

            // Status filter
            if (!string.IsNullOrEmpty(StatusFilter))
            {
                filtered = StatusFilter.ToLower() switch
                {
                    "pending" => filtered.Where(a => a.Status == "Pending" && !a.IsDisbursed),
                    "approved" => filtered.Where(a => a.Status == "Approved" && !a.IsDisbursed),
                    "disbursed" => filtered.Where(a => a.IsDisbursed),
                    "rejected" => filtered.Where(a => a.Status == "Rejected"),
                    _ => filtered
                };
            }

            // Company filter
            if (CompanyFilter.HasValue)
            {
                filtered = filtered.Where(a => a.CompanyID == CompanyFilter.Value);
            }

            // Date range filter
            if (!string.IsNullOrEmpty(DateRange) && int.TryParse(DateRange, out int days))
            {
                var cutoffDate = DateTime.Now.AddDays(-days);
                filtered = filtered.Where(a => a.DateRequested >= cutoffDate);
            }

            return filtered.OrderByDescending(a => a.DateRequested).ToList();
        }

        private void CalculateStatistics(List<Application> allApplications)
        {
            TotalApplications = allApplications.Count;
            PendingApplications = allApplications.Count(a => a.Status == "Pending" && !a.IsDisbursed);
            ApprovedApplications = allApplications.Count(a => a.Status == "Approved");
            TotalAmount = allApplications.Sum(a => a.RequestedCash);
        }
    }
}
