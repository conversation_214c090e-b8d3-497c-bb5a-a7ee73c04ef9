using _Cashdisbursment_.Models;
using Microsoft.Data.SqlClient;

namespace _Cashdisbursment_.Services
{
    public class AcquittalService
    {
        private readonly DatabaseService _dbService;

        public AcquittalService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<List<Acquittal>> GetAcquittalsByApplicationIdAsync(int applicationId)
        {
            const string query = @"
                SELECT a.AcquittalID, a.ApplicationID,
                       app.Description as ApplicationDescription, app.RequestedCash,
                       c.Name as CompanyName
                FROM Acquittals a
                INNER JOIN Applications app ON a.ApplicationID = app.ApplicationID
                INNER JOIN Company c ON app.CompanyID = c.CompanyID
                WHERE a.ApplicationID = @ApplicationID";

            var acquittals = new List<Acquittal>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@ApplicationID", applicationId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                acquittals.Add(new Acquittal
                {
                    AcquittalID = reader.GetInt32(reader.GetOrdinal("AcquittalID")),
                    ApplicationID = reader.GetInt32(reader.GetOrdinal("ApplicationID")),
                    Application = new Application
                    {
                        ApplicationID = reader.GetInt32(reader.GetOrdinal("ApplicationID")),
                        Description = reader.GetString(reader.GetOrdinal("ApplicationDescription")),
                        RequestedCash = reader.GetDouble(reader.GetOrdinal("RequestedCash")),
                        Company = new Company
                        {
                            Name = reader.GetString(reader.GetOrdinal("CompanyName"))
                        }
                    }
                });
            }

            return acquittals;
        }

        public async Task<Acquittal?> GetAcquittalByIdAsync(int acquittalId)
        {
            const string query = @"
                SELECT a.AcquittalID, a.ApplicationID,
                       app.Description, app.Purpose, app.RequestedCash, app.DisbursedCash,
                       app.DateDisbursed, app.CompanyID,
                       c.Name as CompanyName
                FROM Acquittals a
                INNER JOIN Applications app ON a.ApplicationID = app.ApplicationID
                INNER JOIN Company c ON app.CompanyID = c.CompanyID
                WHERE a.AcquittalID = @AcquittalID";

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@AcquittalID", acquittalId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return new Acquittal
                {
                    AcquittalID = reader.GetInt32(reader.GetOrdinal("AcquittalID")),
                    ApplicationID = reader.GetInt32(reader.GetOrdinal("ApplicationID")),
                    Application = new Application
                    {
                        ApplicationID = reader.GetInt32(reader.GetOrdinal("ApplicationID")),
                        Description = reader.GetString(reader.GetOrdinal("Description")),
                        Purpose = reader.GetString(reader.GetOrdinal("Purpose")),
                        RequestedCash = reader.GetDouble(reader.GetOrdinal("RequestedCash")),
                        DisbursedCash = reader.IsDBNull(reader.GetOrdinal("DisbursedCash")) ? null : reader.GetDouble(reader.GetOrdinal("DisbursedCash")),
                        DateDisbursed = reader.IsDBNull(reader.GetOrdinal("DateDisbursed")) ? null : reader.GetDateTime(reader.GetOrdinal("DateDisbursed")),
                        CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                        Company = new Company
                        {
                            CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                            Name = reader.GetString(reader.GetOrdinal("CompanyName"))
                        }
                    }
                };
            }

            return null;
        }

        public async Task<int> CreateAcquittalAsync(int applicationId)
        {
            const string query = @"
                INSERT INTO Acquittals (ApplicationID)
                OUTPUT INSERTED.AcquittalID
                VALUES (@ApplicationID)";

            var parameters = new[]
            {
                new SqlParameter("@ApplicationID", applicationId)
            };

            var acquittalId = await _dbService.ExecuteScalarAsync<int>(query, parameters);
            return acquittalId;
        }



        public async Task<List<AcquittalSubmission>> GetSubmissionsByAcquittalIdAsync(int acquittalId)
        {
            const string query = @"
                SELECT SubmissionID, AcquittalID, Description, Amount, PDFLocation, Date, SubmittedBy
                FROM AcquittalSubmissions
                WHERE AcquittalID = @AcquittalID
                ORDER BY Date DESC";

            var submissions = new List<AcquittalSubmission>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@AcquittalID", acquittalId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                submissions.Add(new AcquittalSubmission
                {
                    SubmissionID = reader.GetInt32(reader.GetOrdinal("SubmissionID")),
                    AcquittalID = reader.GetInt32(reader.GetOrdinal("AcquittalID")),
                    Description = reader.GetString(reader.GetOrdinal("Description")),
                    Amount = reader.GetDouble(reader.GetOrdinal("Amount")),
                    PDFLocation = reader.IsDBNull(reader.GetOrdinal("PDFLocation")) ? null : reader.GetString(reader.GetOrdinal("PDFLocation")),
                    Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                    SubmittedBy = reader.GetString(reader.GetOrdinal("SubmittedBy"))
                });
            }

            return submissions;
        }

        public async Task<int> CreateSubmissionAsync(AcquittalSubmission submission)
        {
            const string query = @"
                INSERT INTO AcquittalSubmissions (AcquittalID, Description, Amount, PDFLocation, Date, SubmittedBy)
                OUTPUT INSERTED.SubmissionID
                VALUES (@AcquittalID, @Description, @Amount, @PDFLocation, @Date, @SubmittedBy)";

            var parameters = new[]
            {
                new SqlParameter("@AcquittalID", submission.AcquittalID),
                new SqlParameter("@Description", submission.Description),
                new SqlParameter("@Amount", submission.Amount),
                new SqlParameter("@PDFLocation", (object?)submission.PDFLocation ?? DBNull.Value),
                new SqlParameter("@Date", submission.Date),
                new SqlParameter("@SubmittedBy", submission.SubmittedBy)
            };

            var submissionId = await _dbService.ExecuteScalarAsync<int>(query, parameters);
            return submissionId;
        }

        public async Task<double> GetTotalSubmittedAmountAsync(int acquittalId)
        {
            const string query = @"
                SELECT ISNULL(SUM(Amount), 0)
                FROM AcquittalSubmissions
                WHERE AcquittalID = @AcquittalID";

            var parameters = new[] { new SqlParameter("@AcquittalID", acquittalId) };
            return await _dbService.ExecuteScalarAsync<double>(query, parameters);
        }

        public async Task<bool> HasAcquittalAsync(int applicationId)
        {
            const string query = "SELECT COUNT(*) FROM Acquittals WHERE ApplicationID = @ApplicationID";
            var parameters = new[] { new SqlParameter("@ApplicationID", applicationId) };
            var count = await _dbService.ExecuteScalarAsync<int>(query, parameters);
            return count > 0;
        }

        public async Task<List<Acquittal>> GetAllAcquittalsAsync()
        {
            const string query = @"
                SELECT a.AcquittalID, a.ApplicationID,
                       app.Description as ApplicationDescription, app.RequestedCash, app.DisbursedCash, app.DateDisbursed,
                       c.Name as CompanyName, c.CompanyID
                FROM Acquittals a
                INNER JOIN Applications app ON a.ApplicationID = app.ApplicationID
                INNER JOIN Company c ON app.CompanyID = c.CompanyID
                WHERE app.IsDisbursed = 1
                ORDER BY app.DateDisbursed DESC";

            var acquittals = new List<Acquittal>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                acquittals.Add(new Acquittal
                {
                    AcquittalID = reader.GetInt32(reader.GetOrdinal("AcquittalID")),
                    ApplicationID = reader.GetInt32(reader.GetOrdinal("ApplicationID")),
                    Application = new Application
                    {
                        ApplicationID = reader.GetInt32(reader.GetOrdinal("ApplicationID")),
                        Description = reader.GetString(reader.GetOrdinal("ApplicationDescription")),
                        RequestedCash = reader.GetDouble(reader.GetOrdinal("RequestedCash")),
                        DisbursedCash = reader.IsDBNull(reader.GetOrdinal("DisbursedCash")) ? null : reader.GetDouble(reader.GetOrdinal("DisbursedCash")),
                        DateDisbursed = reader.IsDBNull(reader.GetOrdinal("DateDisbursed")) ? null : reader.GetDateTime(reader.GetOrdinal("DateDisbursed")),
                        Company = new Company
                        {
                            CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                            Name = reader.GetString(reader.GetOrdinal("CompanyName"))
                        }
                    }
                });
            }

            return acquittals;
        }

        public async Task<List<Acquittal>> GetAcquittalsByCompanyAsync(int companyId)
        {
            const string query = @"
                SELECT a.AcquittalID, a.ApplicationID,
                       app.Description as ApplicationDescription, app.RequestedCash, app.DisbursedCash, app.DateDisbursed,
                       c.Name as CompanyName, c.CompanyID
                FROM Acquittals a
                INNER JOIN Applications app ON a.ApplicationID = app.ApplicationID
                INNER JOIN Company c ON app.CompanyID = c.CompanyID
                WHERE app.IsDisbursed = 1 AND c.CompanyID = @CompanyID
                ORDER BY app.DateDisbursed DESC";

            var acquittals = new List<Acquittal>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@CompanyID", companyId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                acquittals.Add(new Acquittal
                {
                    AcquittalID = reader.GetInt32(reader.GetOrdinal("AcquittalID")),
                    ApplicationID = reader.GetInt32(reader.GetOrdinal("ApplicationID")),
                    Application = new Application
                    {
                        ApplicationID = reader.GetInt32(reader.GetOrdinal("ApplicationID")),
                        Description = reader.GetString(reader.GetOrdinal("ApplicationDescription")),
                        RequestedCash = reader.GetDouble(reader.GetOrdinal("RequestedCash")),
                        DisbursedCash = reader.IsDBNull(reader.GetOrdinal("DisbursedCash")) ? null : reader.GetDouble(reader.GetOrdinal("DisbursedCash")),
                        DateDisbursed = reader.IsDBNull(reader.GetOrdinal("DateDisbursed")) ? null : reader.GetDateTime(reader.GetOrdinal("DateDisbursed")),
                        Company = new Company
                        {
                            CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                            Name = reader.GetString(reader.GetOrdinal("CompanyName"))
                        }
                    }
                });
            }

            return acquittals;
        }
    }
}
