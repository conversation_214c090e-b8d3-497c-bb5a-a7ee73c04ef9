using MailKit.Net.Smtp;
using MimeKit;

namespace _Cashdisbursment_.Services
{
    public class EmailService
    {
        private readonly IConfiguration _configuration;

        public EmailService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true)
        {
            try
            {
                var emailSettings = _configuration.GetSection("EmailSettings");
                var smtpServer = emailSettings["SmtpServer"];
                var senderEmail = emailSettings["SenderEmail"];
                var senderName = emailSettings["SenderName"];
                var password = emailSettings["Password"];

                var message = new MimeMessage();
                message.From.Add(new MailboxAddress(senderName, senderEmail));
                message.To.Add(new MailboxAddress("", toEmail));
                message.Subject = subject;

                var bodyBuilder = new BodyBuilder();
                if (isHtml)
                    bodyBuilder.HtmlBody = body;
                else
                    bodyBuilder.TextBody = body;

                message.Body = bodyBuilder.ToMessageBody();

                using var client = new SmtpClient();
                await client.ConnectAsync(smtpServer, 587, MailKit.Security.SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(senderEmail, password);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);

                return true;
            }
            catch (Exception ex)
            {
                // Log the exception (you might want to use a proper logging framework)
                Console.WriteLine($"Email sending failed: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SendCompanyRegistrationEmailAsync(string companyEmail, string companyName, 
            string userEmail, string password)
        {
            var subject = "Welcome to Zimdef Cash Disbursement System";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Welcome to Zimdef Cash Disbursement System</h2>
                    <p>Dear {companyName},</p>
                    <p>Your company has been successfully registered in our cash disbursement system.</p>
                    <p><strong>Login Credentials:</strong></p>
                    <ul>
                        <li>Email: {userEmail}</li>
                        <li>Password: {password}</li>
                    </ul>
                    <p>Please log in to the system and change your password immediately.</p>
                    <p>You can now submit fund applications through our portal.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement Team</p>
                </body>
                </html>";

            return await SendEmailAsync(companyEmail, subject, body);
        }

        public async Task<bool> SendWelcomeEmailAsync(string companyEmail, string companyName, 
            string userEmail)
        {
            var subject = "Welcome to Zimdef Cash Disbursement System";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Welcome to Zimdef Cash Disbursement System</h2>
                    <p>Dear {companyName},</p>
                    <p>Your company has been successfully registered in our cash disbursement system.</p>
                   
                    <p>You can now submit fund applications through our portal.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement Team</p>
                </body>
                </html>";

            return await SendEmailAsync(companyEmail, subject, body);
        }
        public async Task<bool> SendApplicationStatusEmailAsync(string companyEmail, string companyName,
            int applicationId, string status, string? comment = null)
        {
            var subject = $"Application #{applicationId} Status Update";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Application Status Update</h2>
                    <p>Dear {companyName},</p>
                    <p>Your application #{applicationId} status has been updated to: <strong>{status}</strong></p>
                    {(string.IsNullOrEmpty(comment) ? "" : $"<p><strong>Comment:</strong> {comment}</p>")}
                    <p>Please log in to your portal to view more details.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement Team</p>
                </body>
                </html>";

            return await SendEmailAsync(companyEmail, subject, body);
        }

        public async Task<bool> SendDisbursementNotificationAsync(string companyEmail, string companyName,
            int applicationId, double amount, string disbursedBy)
        {
            var subject = $"Funds Disbursed - Application #{applicationId}";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Funds Disbursed</h2>
                    <p>Dear {companyName},</p>
                    <p>We are pleased to inform you that funds have been disbursed for your application #{applicationId}.</p>
                    <p><strong>Amount Disbursed:</strong> ${amount:N2}</p>
                    <p><strong>Disbursed By:</strong> {disbursedBy}</p>
                    <p><strong>Date:</strong> {DateTime.Now:yyyy-MM-dd HH:mm}</p>
                    <p>Please ensure to submit your acquittal documentation as required.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement Team</p>
                </body>
                </html>";

            return await SendEmailAsync(companyEmail, subject, body);
        }

        public async Task<bool> SendDisbursementNotificationEmailAsync(string companyEmail, string companyName,
            int applicationId, double amount, string description)
        {
            var subject = $"Funds Disbursed - Application #{applicationId}";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Funds Disbursed</h2>
                    <p>Dear {companyName},</p>
                    <p>We are pleased to inform you that funds have been disbursed for your application #{applicationId}.</p>
                    <p><strong>Application Description:</strong> {description}</p>
                    <p><strong>Amount Disbursed:</strong> ${amount:N2}</p>
                    <p><strong>Date:</strong> {DateTime.Now:yyyy-MM-dd HH:mm}</p>
                    <p><strong>Next Steps:</strong></p>
                    <ul>
                        <li>Log in to your portal to view disbursement details</li>
                        <li>Submit acquittal documentation as required</li>
                        <li>Ensure proper use of funds as per application purpose</li>
                    </ul>
                    <p>Please ensure to submit your acquittal documentation as required.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement Team</p>
                </body>
                </html>";

            return await SendEmailAsync(companyEmail, subject, body);
        }

        public async Task<bool> SendApprovalRequestEmailAsync(string approverEmail, int applicationId,
            string companyName, double amount, string description)
        {
            var subject = $"Approval Required - Application #{applicationId}";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Approval Required</h2>
                    <p>Dear Approver,</p>
                    <p>A new application requires your approval:</p>
                    <ul>
                        <li><strong>Application ID:</strong> #{applicationId}</li>
                        <li><strong>Company:</strong> {companyName}</li>
                        <li><strong>Amount:</strong> ${amount:N2}</li>
                        <li><strong>Description:</strong> {description}</li>
                    </ul>
                    <p>Please log in to the admin portal to review and approve/reject this application.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement System</p>
                </body>
                </html>";

            return await SendEmailAsync(approverEmail, subject, body);
        }

        public async Task<bool> SendApproverCredentialsEmailAsync(string approverEmail, string password, int approvalLevel)
        {
            var subject = "Approver Access - Zimdef Cash Disbursement System";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Approver Access Granted</h2>
                    <p>Dear Approver,</p>
                    <p>You have been assigned as a Level {approvalLevel} approver in the Zimdef Cash Disbursement System.</p>
                    <p><strong>Your Login Credentials:</strong></p>
                    <ul>
                        <li><strong>Email:</strong> {approverEmail}</li>
                        <li><strong>Password:</strong> {password}</li>
                        <li><strong>Approval Level:</strong> {approvalLevel}</li>
                    </ul>
                    <p><strong>Important:</strong> Please log in and change your password immediately for security purposes.</p>
                    <p>As an approver, you will have access to:</p>
                    <ul>
                        <li>Review and approve/reject fund applications</li>
                        <li>View application details and supporting documents</li>
                        <li>Add approval comments and feedback</li>
                        <li>Limited read-only access to user and company management</li>
                    </ul>
                    <p>You will receive email notifications when applications require your approval.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement Team</p>
                </body>
                </html>";

            return await SendEmailAsync(approverEmail, subject, body);
        }

        public async Task<bool> SendUserCredentialsEmailAsync(string userEmail, string password, string role)
        {
            var subject = $"{role} Access - Zimdef Cash Disbursement System";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>{role} Access Granted</h2>
                    <p>Dear {role},</p>
                    <p>You have been granted {role.ToLower()} access to the Zimdef Cash Disbursement System.</p>
                    <p><strong>Your Login Credentials:</strong></p>
                    <ul>
                        <li><strong>Email:</strong> {userEmail}</li>
                        <li><strong>Password:</strong> {password}</li>
                        <li><strong>Role:</strong> {role}</li>
                    </ul>
                    <p><strong>Important:</strong> Please log in and change your password immediately for security purposes.</p>
                    <p>As {(role == "Admin" ? "an administrator" : "an approver")}, you will have access to:</p>
                    <ul>";

            if (role == "Admin")
            {
                body += @"
                        <li>Manage companies and users</li>
                        <li>Review and manage all applications</li>
                        <li>Configure system settings</li>
                        <li>View comprehensive reports and analytics</li>";
            }
            else if (role == "Approver")
            {
                body += @"
                        <li>Review and approve/reject fund applications</li>
                        <li>View application details and supporting documents</li>
                        <li>Add approval comments and feedback</li>
                        <li>Limited read-only access to user and company management</li>";
            }

            body += @"
                    </ul>
                    <p>You will receive email notifications when applications require your attention.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement Team</p>
                </body>
                </html>";

            return await SendEmailAsync(userEmail, subject, body);
        }
    }
}
